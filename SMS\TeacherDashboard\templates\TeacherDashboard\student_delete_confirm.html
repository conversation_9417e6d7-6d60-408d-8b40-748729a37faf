{% extends 'TeacherDashboard/base.html' %}
{% load static %}

{% block title %}Delete Student - Teacher Dashboard{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Delete Student</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{% url 'teacher_dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'teacher_student_table' %}">Student Management</a></li>
                        <li class="breadcrumb-item active">Delete Student</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card card-danger">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-exclamation-triangle"></i> 
                                Confirm Student Deletion
                            </h3>
                        </div>
                        
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <h5><i class="icon fas fa-exclamation-triangle"></i> Warning!</h5>
                                You are about to permanently delete this student record. This action cannot be undone.
                            </div>

                            <!-- Student Information -->
                            <div class="row">
                                <div class="col-md-4 text-center">
                                    {% if student.passport %}
                                        <img src="{{ student.passport.url }}" alt="{{ student.fullname }}" 
                                             class="img-fluid img-thumbnail" style="max-width: 200px;">
                                    {% else %}
                                        <div class="bg-light p-4 rounded">
                                            <i class="fas fa-user fa-5x text-muted"></i>
                                            <p class="text-muted mt-2">No Photo</p>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-8">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="40%">Full Name:</th>
                                            <td>{{ student.fullname }}</td>
                                        </tr>
                                        <tr>
                                            <th>Registration Number:</th>
                                            <td>{{ student.registration_number|default:"Not Set" }}</td>
                                        </tr>
                                        <tr>
                                            <th>Class:</th>
                                            <td>{{ student.current_class|default:"Not Assigned" }}</td>
                                        </tr>
                                        <tr>
                                            <th>Section:</th>
                                            <td>{{ student.section|default:"Not Set" }}</td>
                                        </tr>
                                        <tr>
                                            <th>Father's Name:</th>
                                            <td>{{ student.Father_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>Mobile Number:</th>
                                            <td>{{ student.mobile_number|default:"Not Set" }}</td>
                                        </tr>
                                        <tr>
                                            <th>Date of Admission:</th>
                                            <td>{{ student.date_of_admission }}</td>
                                        </tr>
                                        <tr>
                                            <th>Status:</th>
                                            <td>
                                                {% if student.current_status == 'active' %}
                                                    <span class="badge badge-success">Active</span>
                                                {% else %}
                                                    <span class="badge badge-danger">Inactive</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <div class="alert alert-danger mt-3">
                                <h6><i class="fas fa-info-circle"></i> What will be deleted:</h6>
                                <ul class="mb-0">
                                    <li>Student's personal information</li>
                                    <li>Academic records and class assignments</li>
                                    <li>Attendance records</li>
                                    <li>Exam marks and results</li>
                                    <li>Fee payment records</li>
                                    <li>All associated documents and photos</li>
                                </ul>
                            </div>
                        </div>

                        <div class="card-footer text-right">
                            <form method="post" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger mr-2">
                                    <i class="fas fa-trash"></i> Yes, Delete Student
                                </button>
                            </form>
                            <a href="{% url 'teacher_student_detail' student.pk %}" class="btn btn-secondary mr-2">
                                <i class="fas fa-eye"></i> View Student Details
                            </a>
                            <a href="{% url 'teacher_student_table' %}" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> Back to Student List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation dialog
    $('form').on('submit', function(e) {
        if (!confirm('Are you absolutely sure you want to delete this student? This action cannot be undone!')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
