{% extends 'TeacherDashboard/base.html' %}
{% load static %}

{% block title %}Student Management - Teacher Dashboard{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Student Management</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{% url 'teacher_dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Student Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Action Buttons -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            <a href="{% url 'teacher_student_create' %}" class="btn btn-primary mr-2">
                                <i class="fas fa-plus"></i> Admit New Student
                            </a>
                            <a href="{% url 'teacher_student_udise_create' %}" class="btn btn-info mr-2">
                                <i class="fas fa-file-alt"></i> Admit Student (UDISE Format)
                            </a>
                            <a href="{% url 'teacher_students_list' %}" class="btn btn-secondary">
                                <i class="fas fa-list"></i> Simple Student List
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Search & Filter</h3>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row">
                                <div class="col-md-3">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Search by name, registration, father name..." 
                                           value="{{ search_query }}">
                                </div>
                                <div class="col-md-2">
                                    <select name="class_filter" class="form-control">
                                        <option value="">All Classes</option>
                                        {% for class in classes %}
                                            <option value="{{ class.id }}" {% if class_filter == class.id|stringformat:"s" %}selected{% endif %}>
                                                {{ class.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="status_filter" class="form-control">
                                        <option value="">All Status</option>
                                        <option value="active" {% if status_filter == "active" %}selected{% endif %}>Active</option>
                                        <option value="inactive" {% if status_filter == "inactive" %}selected{% endif %}>Inactive</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <a href="{% url 'teacher_student_table' %}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Students Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Students ({{ total_students }} total)</h3>
                        </div>
                        <div class="card-body table-responsive p-0">
                            <table class="table table-hover text-nowrap">
                                <thead>
                                    <tr>
                                        <th>Registration No.</th>
                                        <th>Student Name</th>
                                        <th>Class</th>
                                        <th>Section</th>
                                        <th>Father Name</th>
                                        <th>Mobile</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for student in students %}
                                    <tr>
                                        <td>{{ student.registration_number|default:"Not Set" }}</td>
                                        <td>
                                            <a href="{% url 'teacher_student_detail' student.pk %}" class="text-decoration-none">
                                                {{ student.fullname }}
                                            </a>
                                        </td>
                                        <td>{{ student.current_class|default:"Not Assigned" }}</td>
                                        <td>{{ student.section|default:"Not Set" }}</td>
                                        <td>{{ student.Father_name }}</td>
                                        <td>{{ student.mobile_number|default:"Not Set" }}</td>
                                        <td>
                                            {% if student.current_status == 'active' %}
                                                <span class="badge badge-success">Active</span>
                                            {% else %}
                                                <span class="badge badge-danger">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'teacher_student_detail' student.pk %}" 
                                                   class="btn btn-sm btn-info" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'teacher_student_edit' student.pk %}" 
                                                   class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'teacher_student_delete' student.pk %}" 
                                                   class="btn btn-sm btn-danger" title="Delete"
                                                   onclick="return confirm('Are you sure you want to delete this student?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="8" class="text-center">
                                            <div class="alert alert-info">
                                                No students found. 
                                                <a href="{% url 'teacher_student_create' %}">Add the first student</a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        {% if students.has_other_pages %}
                        <div class="card-footer clearfix">
                            <ul class="pagination pagination-sm m-0 float-right">
                                {% if students.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ students.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if class_filter %}&class_filter={{ class_filter }}{% endif %}{% if status_filter %}&status_filter={{ status_filter }}{% endif %}">&laquo;</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in students.paginator.page_range %}
                                    {% if students.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > students.number|add:'-3' and num < students.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if class_filter %}&class_filter={{ class_filter }}{% endif %}{% if status_filter %}&status_filter={{ status_filter }}{% endif %}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if students.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ students.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if class_filter %}&class_filter={{ class_filter }}{% endif %}{% if status_filter %}&status_filter={{ status_filter }}{% endif %}">&raquo;</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-submit form on filter change
    $('select[name="class_filter"], select[name="status_filter"]').change(function() {
        $(this).closest('form').submit();
    });
});
</script>
{% endblock %}
