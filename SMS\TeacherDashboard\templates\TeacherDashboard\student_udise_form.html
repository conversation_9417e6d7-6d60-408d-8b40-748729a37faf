{% extends 'TeacherDashboard/base.html' %}
{% load static %}

{% block title %}{{ title }} - Teacher Dashboard{% endblock %}

{% block extra_css %}
<style>
.udise-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.udise-field-group {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.udise-field-label {
    font-weight: 600;
    color: #2c3e50;
    display: block;
    margin-bottom: 5px;
}

.udise-field-number {
    background: #3498db;
    color: white;
    padding: 2px 8px;
    border-radius: 50%;
    font-size: 12px;
    margin-right: 8px;
}

.udise-rule-reference {
    font-size: 11px;
    color: #7f8c8d;
    font-style: italic;
}

.field-validation-indicator {
    float: right;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
}

.field-required {
    background: #e74c3c;
    color: white;
}

.field-optional {
    background: #95a5a6;
    color: white;
}

.udise-section-header {
    background: #34495e;
    color: white;
    padding: 15px;
    margin: 20px 0 10px 0;
    border-radius: 5px;
    font-weight: bold;
}
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header udise-form">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ title }}</h1>
                    <p class="mb-0">UDISE+ Compliant Student Admission Form</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{% url 'teacher_dashboard' %}" class="text-white">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'teacher_student_table' %}" class="text-white">Student Management</a></li>
                        <li class="breadcrumb-item active text-white">{{ title }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h3 class="card-title">
                                <i class="fas fa-file-alt"></i> UDISE+ Student Admission Form
                            </h3>
                            <div class="card-tools">
                                <a href="{% url 'teacher_student_table' %}" class="btn btn-light btn-sm">
                                    <i class="fas fa-arrow-left"></i> Back to Student List
                                </a>
                            </div>
                        </div>
                        
                        <form method="post" enctype="multipart/form-data">
                            {% csrf_token %}
                            <div class="card-body">
                                {% if form.non_field_errors %}
                                    <div class="alert alert-danger">
                                        {{ form.non_field_errors }}
                                    </div>
                                {% endif %}

                                <!-- Section 1: Basic Information -->
                                <div class="udise-section-header">
                                    <i class="fas fa-user"></i> Section 1: Basic Student Information
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-required">Required</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">1</span> Student Full Name
                                                <span class="udise-rule-reference">(As per official documents)</span>
                                            </label>
                                            {{ form.fullname }}
                                            {% if form.fullname.errors %}
                                                <div class="text-danger mt-1">{{ form.fullname.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-required">Required</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">2</span> Gender
                                            </label>
                                            {{ form.gender }}
                                            {% if form.gender.errors %}
                                                <div class="text-danger mt-1">{{ form.gender.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-required">Required</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">3</span> Date of Birth
                                            </label>
                                            {{ form.date_of_birth }}
                                            {% if form.date_of_birth.errors %}
                                                <div class="text-danger mt-1">{{ form.date_of_birth.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-optional">Optional</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">4</span> Category
                                                <span class="udise-rule-reference">(Social Category)</span>
                                            </label>
                                            {{ form.category }}
                                            {% if form.category.errors %}
                                                <div class="text-danger mt-1">{{ form.category.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-optional">Optional</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">5</span> Aadhaar Number
                                                <span class="udise-rule-reference">(12 digits)</span>
                                            </label>
                                            {{ form.aadhar }}
                                            {% if form.aadhar.errors %}
                                                <div class="text-danger mt-1">{{ form.aadhar.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-optional">Optional</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">6</span> Mobile Number
                                            </label>
                                            {{ form.mobile_number }}
                                            {% if form.mobile_number.errors %}
                                                <div class="text-danger mt-1">{{ form.mobile_number.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Section 2: Academic Information -->
                                <div class="udise-section-header">
                                    <i class="fas fa-graduation-cap"></i> Section 2: Academic Information
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-required">Required</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">7</span> Date of Admission
                                            </label>
                                            {{ form.date_of_admission }}
                                            {% if form.date_of_admission.errors %}
                                                <div class="text-danger mt-1">{{ form.date_of_admission.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-required">Required</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">8</span> Class
                                            </label>
                                            {{ form.current_class }}
                                            {% if form.current_class.errors %}
                                                <div class="text-danger mt-1">{{ form.current_class.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-optional">Optional</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">9</span> Section
                                            </label>
                                            {{ form.section }}
                                            {% if form.section.errors %}
                                                <div class="text-danger mt-1">{{ form.section.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Section 3: Parent Information -->
                                <div class="udise-section-header">
                                    <i class="fas fa-users"></i> Section 3: Parent/Guardian Information
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-required">Required</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">10</span> Father's Name
                                            </label>
                                            {{ form.Father_name }}
                                            {% if form.Father_name.errors %}
                                                <div class="text-danger mt-1">{{ form.Father_name.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-required">Required</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">11</span> Father's Mobile Number
                                            </label>
                                            {{ form.Father_mobile_number }}
                                            {% if form.Father_mobile_number.errors %}
                                                <div class="text-danger mt-1">{{ form.Father_mobile_number.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-optional">Optional</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">12</span> Mother's Name
                                            </label>
                                            {{ form.Mother_name }}
                                            {% if form.Mother_name.errors %}
                                                <div class="text-danger mt-1">{{ form.Mother_name.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-optional">Optional</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">13</span> Father's Aadhaar
                                            </label>
                                            {{ form.Father_aadhar }}
                                            {% if form.Father_aadhar.errors %}
                                                <div class="text-danger mt-1">{{ form.Father_aadhar.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Section 4: Additional Information -->
                                <div class="udise-section-header">
                                    <i class="fas fa-info-circle"></i> Section 4: Additional Information
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-optional">Optional</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">14</span> Address
                                            </label>
                                            {{ form.address }}
                                            {% if form.address.errors %}
                                                <div class="text-danger mt-1">{{ form.address.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-optional">Optional</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">15</span> Student Photo
                                            </label>
                                            {{ form.passport }}
                                            {% if form.passport.errors %}
                                                <div class="text-danger mt-1">{{ form.passport.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="udise-field-group">
                                            <span class="field-validation-indicator field-optional">Optional</span>
                                            <label class="udise-field-label">
                                                <span class="udise-field-number">16</span> Other Information
                                                <span class="udise-rule-reference">(Special needs, medical conditions, etc.)</span>
                                            </label>
                                            {{ form.others }}
                                            {% if form.others.errors %}
                                                <div class="text-danger mt-1">{{ form.others.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer bg-light">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i> 
                                            This form follows UDISE+ guidelines for student data collection.
                                        </small>
                                    </div>
                                    <div class="col-md-6 text-right">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save"></i> 
                                            Admit Student (UDISE Format)
                                        </button>
                                        <a href="{% url 'teacher_student_table' %}" class="btn btn-secondary btn-lg ml-2">
                                            <i class="fas fa-times"></i> Cancel
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Load sections when class changes
function loadSections(classId) {
    if (classId) {
        fetch(`/teacher-dashboard/api/class/${classId}/sections/`)
            .then(response => response.json())
            .then(data => {
                const sectionSelect = document.querySelector('select[name="section"]');
                sectionSelect.innerHTML = '<option value="">-- Select Section --</option>';
                
                if (data.sections) {
                    data.sections.forEach(section => {
                        const option = document.createElement('option');
                        option.value = section;
                        option.textContent = section;
                        sectionSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading sections:', error);
            });
    }
}

// Add event listener to class select
document.addEventListener('DOMContentLoaded', function() {
    const classSelect = document.querySelector('select[name="current_class"]');
    if (classSelect) {
        classSelect.addEventListener('change', function() {
            loadSections(this.value);
        });
    }
});
</script>
{% endblock %}
