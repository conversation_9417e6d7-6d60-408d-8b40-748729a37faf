from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from apps.students.models import Student, StudentUDISEInfo
from apps.students.forms import StudentForm
from apps.staffs.models import Staff
from apps.attendance.models import Attendance
from apps.exams.models import Exam, Mark
from apps.documents.models import Document
from apps.corecode.models import StudentClass, Section


@login_required
def teacher_dashboard(request):
    """Main teacher dashboard"""
    try:
        # Get the staff object for the logged-in user
        staff = Staff.objects.get(user=request.user)

        # Get teacher's assigned classes/students (you may need to adjust this based on your model structure)
        teacher_students = Student.objects.filter(current_status='active')[:10]  # Limit for dashboard
        recent_exams = Exam.objects.all()[:5]

        context = {
            'staff': staff,
            'teacher_students': teacher_students,
            'recent_exams': recent_exams,
            'total_students': teacher_students.count(),
        }
        return render(request, 'TeacherDashboard/dashboard.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found. Please contact administrator.')
        return render(request, 'TeacherDashboard/dashboard.html', {})


@login_required
def teacher_students_list(request):
    """Teacher-specific student list"""
    try:
        staff = Staff.objects.get(user=request.user)
        # Get students assigned to this teacher (adjust query as needed)
        students = Student.objects.filter(current_status='active')

        context = {
            'students': students,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/students_list.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/students_list.html', {})


@login_required
def teacher_student_detail(request, pk):
    """Teacher-specific student detail view"""
    student = get_object_or_404(Student, pk=pk)
    try:
        staff = Staff.objects.get(user=request.user)
        context = {
            'student': student,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/student_detail.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/student_detail.html', {'student': student})


@login_required
def teacher_attendance_list(request):
    """Teacher-specific attendance management"""
    try:
        staff = Staff.objects.get(user=request.user)
        # Get attendance records for teacher's classes
        attendance_records = Attendance.objects.all()[:20]  # Adjust query as needed

        context = {
            'attendance_records': attendance_records,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/attendance_list.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/attendance_list.html', {})


@login_required
def teacher_attendance_mark(request):
    """Teacher attendance marking interface"""
    try:
        staff = Staff.objects.get(user=request.user)
        students = Student.objects.filter(current_status='active')

        context = {
            'students': students,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/attendance_mark.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/attendance_mark.html', {})


@login_required
def teacher_exams_list(request):
    """Teacher-specific exams list"""
    try:
        staff = Staff.objects.get(user=request.user)
        exams = Exam.objects.all()

        context = {
            'exams': exams,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/exams_list.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/exams_list.html', {})


@login_required
def teacher_marks_entry(request):
    """Teacher marks entry interface"""
    try:
        staff = Staff.objects.get(user=request.user)
        exams = Exam.objects.all()
        students = Student.objects.filter(current_status='active')

        context = {
            'exams': exams,
            'students': students,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/marks_entry.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/marks_entry.html', {})


@login_required
def teacher_documents_list(request):
    """Teacher-specific documents"""
    try:
        staff = Staff.objects.get(user=request.user)
        documents = Document.objects.all()[:20]  # Adjust query as needed

        context = {
            'documents': documents,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/documents_list.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/documents_list.html', {})


@login_required
def teacher_profile(request):
    """Teacher profile management"""
    try:
        staff = Staff.objects.get(user=request.user)

        context = {
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/profile.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/profile.html', {})


# ============ FULL STUDENT MANAGEMENT VIEWS ============

@login_required
def teacher_student_table(request):
    """Full student table view with search and pagination for teachers"""
    try:
        staff = Staff.objects.get(user=request.user)

        # Get all students
        students = Student.objects.all().order_by('fullname')

        # Search functionality
        search_query = request.GET.get('search', '')
        if search_query:
            students = students.filter(
                Q(fullname__icontains=search_query) |
                Q(registration_number__icontains=search_query) |
                Q(Father_name__icontains=search_query) |
                Q(mobile_number__icontains=search_query)
            )

        # Class filter
        class_filter = request.GET.get('class_filter', '')
        if class_filter:
            students = students.filter(current_class_id=class_filter)

        # Status filter
        status_filter = request.GET.get('status_filter', '')
        if status_filter:
            students = students.filter(current_status=status_filter)

        # Pagination
        paginator = Paginator(students, 25)  # Show 25 students per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Get all classes for filter dropdown
        classes = StudentClass.objects.all()

        context = {
            'staff': staff,
            'students': page_obj,
            'search_query': search_query,
            'class_filter': class_filter,
            'status_filter': status_filter,
            'classes': classes,
            'total_students': students.count(),
        }
        return render(request, 'TeacherDashboard/student_table.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/student_table.html', {})


@login_required
def teacher_student_create(request):
    """Teacher student admission/creation view"""
    try:
        staff = Staff.objects.get(user=request.user)

        if request.method == 'POST':
            form = StudentForm(request.POST, request.FILES)
            if form.is_valid():
                student = form.save()
                messages.success(request, f'Student {student.fullname} has been successfully admitted!')
                return redirect('teacher_student_detail', pk=student.pk)
            else:
                messages.error(request, 'Please correct the errors below.')
        else:
            form = StudentForm()

        context = {
            'staff': staff,
            'form': form,
            'title': 'Admit New Student',
        }
        return render(request, 'TeacherDashboard/student_form.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/student_form.html', {})


@login_required
def teacher_student_edit(request, pk):
    """Teacher student edit view"""
    student = get_object_or_404(Student, pk=pk)
    try:
        staff = Staff.objects.get(user=request.user)

        if request.method == 'POST':
            form = StudentForm(request.POST, request.FILES, instance=student)
            if form.is_valid():
                student = form.save()
                messages.success(request, f'Student {student.fullname} has been successfully updated!')
                return redirect('teacher_student_detail', pk=student.pk)
            else:
                messages.error(request, 'Please correct the errors below.')
        else:
            form = StudentForm(instance=student)

        context = {
            'staff': staff,
            'form': form,
            'student': student,
            'title': f'Edit Student - {student.fullname}',
        }
        return render(request, 'TeacherDashboard/student_form.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/student_form.html', {'student': student})


@login_required
def teacher_student_delete(request, pk):
    """Teacher student delete view"""
    student = get_object_or_404(Student, pk=pk)
    try:
        staff = Staff.objects.get(user=request.user)

        if request.method == 'POST':
            student_name = student.fullname
            student.delete()
            messages.success(request, f'Student {student_name} has been successfully deleted!')
            return redirect('teacher_student_table')

        context = {
            'staff': staff,
            'student': student,
        }
        return render(request, 'TeacherDashboard/student_delete_confirm.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/student_delete_confirm.html', {'student': student})


@login_required
def teacher_student_udise_create(request):
    """Teacher UDISE student admission view"""
    try:
        staff = Staff.objects.get(user=request.user)

        if request.method == 'POST':
            form = StudentForm(request.POST, request.FILES)
            if form.is_valid():
                student = form.save()

                # Create UDISE info if needed
                if not hasattr(student, 'udise_info'):
                    StudentUDISEInfo.objects.create(student=student)

                messages.success(request, f'Student {student.fullname} has been successfully admitted using UDISE format!')
                return redirect('teacher_student_detail', pk=student.pk)
            else:
                messages.error(request, 'Please correct the errors below.')
        else:
            form = StudentForm()

        context = {
            'staff': staff,
            'form': form,
            'title': 'Admit New Student (UDISE Format)',
            'is_udise': True,
        }
        return render(request, 'TeacherDashboard/student_udise_form.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/student_udise_form.html', {})


@login_required
def teacher_get_sections_for_class(request, class_id):
    """AJAX endpoint to get sections for a specific class"""
    try:
        sections = Section.objects.filter(
            student_class_id=class_id,
            is_active=True
        ).values_list('name', flat=True)

        return JsonResponse({
            'sections': list(sections)
        })
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)
