{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-tachometer-alt"></i> Teacher  Dashboard
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-tachometer-alt{% endblock title-icon %}

{% block title %} Teacher Dashboard{% endblock title %}

{% block subtitle %}Welcome to the school management system dashboard{% endblock subtitle %}

{% block page-actions %}
<a href="{% url 'current-session' %}" class="btn btn-sm btn-outline-primary">
  <i class="fas fa-calendar-alt"></i> Current Session: {{ current_session }}
</a>
{% endblock page-actions %}

{% block content %}
<div class="container mt-4">
  <!-- Quick Actions Row -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-bolt me-2"></i> Quick Actions</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3 mb-2">
              <a href="{% url 'teacher_student_table' %}" class="btn btn-outline-primary btn-block">
                <i class="fas fa-table"></i> All Students
              </a>
            </div>
            <div class="col-md-3 mb-2">
              <a href="{% url 'teacher_student_create' %}" class="btn btn-outline-success btn-block">
                <i class="fas fa-plus"></i> Admit Student
              </a>
            </div>
            <div class="col-md-3 mb-2">
              <a href="{% url 'teacher_attendance_mark' %}" class="btn btn-outline-warning btn-block">
                <i class="fas fa-calendar-check"></i> Mark Attendance
              </a>
            </div>
            <div class="col-md-3 mb-2">
              <a href="{% url 'teacher_marks_entry' %}" class="btn btn-outline-info btn-block">
                <i class="fas fa-clipboard-check"></i> Enter Marks
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0"><i class="fas fa-users me-2"></i> Student Overview</h5>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-md-4">
              <div class="info-box bg-info">
                <span class="info-box-icon"><i class="fas fa-user-graduate"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">Total Students</span>
                  <span class="info-box-number">{{ total_students|default:0 }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="info-box bg-success">
                <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">Active Students</span>
                  <span class="info-box-number">{{ total_students|default:0 }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="info-box bg-warning">
                <span class="info-box-icon"><i class="fas fa-calendar-alt"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">Recent Students</span>
                  <span class="info-box-number">{{ teacher_students|length|default:0 }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-3">
            <h6>Recent Students:</h6>
            <ul class="list-group">
              {% for student in teacher_students|slice:":5" %}
                <li class="list-group-item d-flex justify-content-between align-items-center">
                  <a href="{% url 'teacher_student_detail' student.pk %}" class="text-decoration-none">
                    {{ student.fullname }}
                  </a>
                  <span class="badge bg-primary">{{ student.current_class|default:"No Class" }}</span>
                </li>
              {% empty %}
                <li class="list-group-item">No students found. <a href="{% url 'teacher_student_create' %}">Add the first student</a></li>
              {% endfor %}
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="fas fa-tasks me-2"></i> Recent Assignments</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for assignment in teacher_assignments %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ assignment.title }}
                <span class="badge bg-secondary">Due: {{ assignment.due_date }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No assignments found.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i> Upcoming Events</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for event in teacher_events %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ event.title }}
                <span class="badge bg-info">{{ event.date }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No upcoming events.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i> Gradebook</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for gradebook in teacher_gradebooks %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ gradebook.class_name }}
                <span class="badge bg-success">{{ gradebook.entries_count }} Entries</span>
              </li>
            {% empty %}
              <li class="list-group-item">No gradebook entries.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}