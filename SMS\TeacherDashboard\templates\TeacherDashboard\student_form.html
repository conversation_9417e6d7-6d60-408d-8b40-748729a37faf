{% extends 'TeacherDashboard/base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}
  {% if student %}
    Update Student - Teacher Dashboard
  {% else %}
    Add New Student - Teacher Dashboard
  {% endif %}
{% endblock title %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_student_table' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-user-graduate"></i> Students
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-plus-circle"></i> 
        {% if student %}Update{% else %}Add New{% endif %} Student
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas {% if student %}fa-edit{% else %}fa-user-plus{% endif %}{% endblock title-icon %}

{% block title %}
  {% if student %}
    Update {{ student.fullname }}
  {% else %}
    Add New Student
  {% endif %}
{% endblock title %}

{% block subtitle %}
  {% if student %}
    Update student information
  {% else %}
    Register a new student in the system
  {% endif %}
{% endblock subtitle %}

{% block page-actions %}
<a class="btn btn-outline-secondary" href="{% url 'teacher_student_table' %}">
  <i class="fas fa-arrow-left"></i> Back to List
</a>
{% endblock page-actions %}

{% block content %}
<div class="container-fluid px-0">
  <div class="card shadow-sm border-0 mb-4">
    <div class="card-header bg-gradient-primary text-white">
      <h5 class="mb-0">
        <i class="fas {% if student %}fa-edit{% else %}fa-user-plus{% endif %} me-2"></i>
        {% if student %}
          Update Student Information
        {% else %}
          New Student Registration
        {% endif %}
      </h5>
    </div>
    <div class="card-body">
      <form action="" method="POST" enctype="multipart/form-data" id="studentForm" class="needs-validation" novalidate>
        {% csrf_token %}

        {% if form.non_field_errors %}
          <div class="alert alert-danger">
            {{ form.non_field_errors }}
          </div>
        {% endif %}

        <!-- Personal Information Section -->
        <div class="card mb-4 border-0 shadow-sm">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-9">
                <div class="row">
                  <!-- Fullname Field -->
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.fullname.id_for_label }}" class="form-label">
                      <i class="fas fa-user-tag me-1"></i> {{ form.fullname.label }} <span class="text-danger">*</span>
                    </label>
                    {{ form.fullname|add_class:"form-control" }}
                    {% if form.fullname.errors %}
                      <div class="text-danger small">{{ form.fullname.errors }}</div>
                    {% endif %}
                  </div>

                  <!-- Gender Field -->
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.gender.id_for_label }}" class="form-label">
                      <i class="fas fa-venus-mars me-1"></i> {{ form.gender.label }}
                    </label>
                    {{ form.gender|add_class:"form-select" }}
                    {% if form.gender.errors %}
                      <div class="text-danger small">{{ form.gender.errors }}</div>
                    {% endif %}
                  </div>

                  <!-- Date of Birth Field -->
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                      <i class="fas fa-birthday-cake me-1"></i> {{ form.date_of_birth.label }}
                    </label>
                    {{ form.date_of_birth|add_class:"form-control" }}
                    {% if form.date_of_birth.errors %}
                      <div class="text-danger small">{{ form.date_of_birth.errors }}</div>
                    {% endif %}
                  </div>

                  <!-- Category Field -->
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.category.id_for_label }}" class="form-label">
                      <i class="fas fa-tag me-1"></i> {{ form.category.label }}
                    </label>
                    {{ form.category|add_class:"form-select" }}
                    {% if form.category.errors %}
                      <div class="text-danger small">{{ form.category.errors }}</div>
                    {% endif %}
                  </div>

                  <!-- Aadhar Field -->
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.aadhar.id_for_label }}" class="form-label">
                      <i class="fas fa-id-card me-1"></i> {{ form.aadhar.label }}
                    </label>
                    {{ form.aadhar|add_class:"form-control" }}
                    <div class="form-text">12-digit Aadhar number without spaces</div>
                    {% if form.aadhar.errors %}
                      <div class="text-danger small">{{ form.aadhar.errors }}</div>
                    {% endif %}
                  </div>

                  <!-- Current Status Field -->
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.current_status.id_for_label }}" class="form-label">
                      <i class="fas fa-toggle-on me-1"></i> {{ form.current_status.label }}
                    </label>
                    {{ form.current_status|add_class:"form-select" }}
                    {% if form.current_status.errors %}
                      <div class="text-danger small">{{ form.current_status.errors }}</div>
                    {% endif %}
                  </div>
                </div>
              </div>

              <!-- Photo Upload -->
              <div class="col-md-3">
                <div class="text-center mb-3">
                  <div class="mb-3">
                    <label for="{{ form.passport.id_for_label }}" class="form-label d-block">
                      <i class="fas fa-camera me-1"></i> Student Photo
                    </label>
                    <div class="photo-preview mb-2">
                      {% if student and student.passport %}
                        <img src="{{ student.passport.url }}" alt="Student Photo" class="img-thumbnail" style="height: 150px; width: 150px; object-fit: cover;">
                      {% else %}
                        <div class="placeholder-image d-flex align-items-center justify-content-center bg-light" style="height: 150px; width: 150px; border-radius: 5px;">
                          <i class="fas fa-user fa-4x text-secondary"></i>
                        </div>
                      {% endif %}
                    </div>
                    {{ form.passport|add_class:"form-control" }}
                    <div class="form-text">Upload passport size photo</div>
                    {% if form.passport.errors %}
                      <div class="text-danger small">{{ form.passport.errors }}</div>
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Academic Information Section -->
        <div class="card mb-4 border-0 shadow-sm">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Academic Information</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Class Field -->
              <div class="col-md-4 mb-3">
                <label for="{{ form.current_class.id_for_label }}" class="form-label">
                  <i class="fas fa-chalkboard me-1"></i> CLASS <span class="text-danger">*</span>
                </label>
                {{ form.current_class|add_class:"form-select" }}
                {% if form.current_class.errors %}
                  <div class="text-danger small">{{ form.current_class.errors }}</div>
                {% endif %}
              </div>

              <!-- Section Field -->
              <div class="col-md-4 mb-3">
                <label for="{{ form.section.id_for_label }}" class="form-label">
                  <i class="fas fa-puzzle-piece me-1"></i> {{ form.section.label }}
                </label>
                {{ form.section|add_class:"form-select" }}
                {% if form.section.errors %}
                  <div class="text-danger small">{{ form.section.errors }}</div>
                {% endif %}
              </div>

              <!-- Date of Admission Field -->
              <div class="col-md-4 mb-3">
                <label for="{{ form.date_of_admission.id_for_label }}" class="form-label">
                  <i class="fas fa-calendar-check me-1"></i> {{ form.date_of_admission.label }}
                </label>
                {{ form.date_of_admission|add_class:"form-control" }}
                {% if form.date_of_admission.errors %}
                  <div class="text-danger small">{{ form.date_of_admission.errors }}</div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information Section -->
        <div class="card mb-4 border-0 shadow-sm">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-address-book me-2"></i>Contact Information</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Mobile Number Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.mobile_number.id_for_label }}" class="form-label">
                  <i class="fas fa-mobile-alt me-1"></i> {{ form.mobile_number.label }}
                </label>
                {{ form.mobile_number|add_class:"form-control" }}
                {% if form.mobile_number.errors %}
                  <div class="text-danger small">{{ form.mobile_number.errors }}</div>
                {% endif %}
              </div>

              <!-- Email Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.email_id.id_for_label }}" class="form-label">
                  <i class="fas fa-envelope me-1"></i> {{ form.email_id.label }}
                </label>
                {{ form.email_id|add_class:"form-control" }}
                {% if form.email_id.errors %}
                  <div class="text-danger small">{{ form.email_id.errors }}</div>
                {% endif %}
              </div>

              <!-- Address Field -->
              <div class="col-md-12 mb-3">
                <label for="{{ form.address.id_for_label }}" class="form-label">
                  <i class="fas fa-map-marker-alt me-1"></i> {{ form.address.label }} <span class="text-danger">*</span>
                </label>
                {{ form.address|add_class:"form-control" }}
                {% if form.address.errors %}
                  <div class="text-danger small">{{ form.address.errors }}</div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Family Information Section -->
        <div class="card mb-4 border-0 shadow-sm">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-users me-2"></i>Family Information</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Father's Name Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.Father_name.id_for_label }}" class="form-label">
                  <i class="fas fa-user me-1"></i> {{ form.Father_name.label }} <span class="text-danger">*</span>
                </label>
                {{ form.Father_name|add_class:"form-control" }}
                {% if form.Father_name.errors %}
                  <div class="text-danger small">{{ form.Father_name.errors }}</div>
                {% endif %}
              </div>

              <!-- Father's Mobile Number Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.Father_mobile_number.id_for_label }}" class="form-label">
                  <i class="fas fa-phone me-1"></i> {{ form.Father_mobile_number.label }} <span class="text-danger">*</span>
                </label>
                {{ form.Father_mobile_number|add_class:"form-control" }}
                {% if form.Father_mobile_number.errors %}
                  <div class="text-danger small">{{ form.Father_mobile_number.errors }}</div>
                {% endif %}
              </div>

              <!-- Father's Aadhar Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.Father_aadhar.id_for_label }}" class="form-label">
                  <i class="fas fa-id-card me-1"></i> {{ form.Father_aadhar.label }}
                </label>
                {{ form.Father_aadhar|add_class:"form-control" }}
                <div class="form-text">12-digit Aadhar number without spaces</div>
                {% if form.Father_aadhar.errors %}
                  <div class="text-danger small">{{ form.Father_aadhar.errors }}</div>
                {% endif %}
              </div>

              <!-- Mother's Name Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.Mother_name.id_for_label }}" class="form-label">
                  <i class="fas fa-user me-1"></i> {{ form.Mother_name.label }}
                </label>
                {{ form.Mother_name|add_class:"form-control" }}
                {% if form.Mother_name.errors %}
                  <div class="text-danger small">{{ form.Mother_name.errors }}</div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Information Section -->
        <div class="card mb-4 border-0 shadow-sm">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Additional Information</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Others Field -->
              <div class="col-md-12 mb-3">
                <label for="{{ form.others.id_for_label }}" class="form-label">
                  <i class="fas fa-sticky-note me-1"></i> {{ form.others.label }}
                </label>
                {{ form.others|add_class:"form-control" }}
                <div class="form-text">Any additional notes or information about the student</div>
                {% if form.others.errors %}
                  <div class="text-danger small">{{ form.others.errors }}</div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-between align-items-center mt-4">
          <div>
            <small class="text-muted">
              <i class="fas fa-info-circle me-1"></i>
              Fields marked with <span class="text-danger">*</span> are required
            </small>
          </div>
          <div>
            <a href="{% url 'teacher_student_table' %}" class="btn btn-outline-secondary me-2">
              <i class="fas fa-times me-1"></i> Cancel
            </a>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save me-1"></i>
              {% if student %}
                Update Student
              {% else %}
                Add Student
              {% endif %}
            </button>
          </div>
        </div>

      </form>
    </div>
  </div>
</div>

<script>
// Load sections when class changes
function loadSections(classId) {
    if (classId) {
        fetch(`/teacher-dashboard/api/class/${classId}/sections/`)
            .then(response => response.json())
            .then(data => {
                const sectionSelect = document.querySelector('select[name="section"]');
                sectionSelect.innerHTML = '<option value="">-- Select Section --</option>';

                if (data.sections) {
                    data.sections.forEach(section => {
                        const option = document.createElement('option');
                        option.value = section;
                        option.textContent = section;
                        sectionSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading sections:', error);
            });
    }
}

// Add event listener to class select
document.addEventListener('DOMContentLoaded', function() {
    const classSelect = document.querySelector('select[name="current_class"]');
    if (classSelect) {
        classSelect.addEventListener('change', function() {
            loadSections(this.value);
        });
    }
});
</script>
{% endblock content %}
