{% extends 'TeacherDashboard/base.html' %}
{% load static %}

{% block title %}{{ title }} - Teacher Dashboard{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ title }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{% url 'teacher_dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'teacher_student_table' %}">Student Management</a></li>
                        <li class="breadcrumb-item active">{{ title }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">{{ title }}</h3>
                            <div class="card-tools">
                                <a href="{% url 'teacher_student_table' %}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-arrow-left"></i> Back to Student List
                                </a>
                            </div>
                        </div>
                        
                        <form method="post" enctype="multipart/form-data">
                            {% csrf_token %}
                            <div class="card-body">
                                {% if form.non_field_errors %}
                                    <div class="alert alert-danger">
                                        {{ form.non_field_errors }}
                                    </div>
                                {% endif %}

                                <div class="row">
                                    <!-- Personal Information -->
                                    <div class="col-md-6">
                                        <h5 class="text-primary mb-3">Personal Information</h5>
                                        
                                        <div class="form-group">
                                            <label for="{{ form.fullname.id_for_label }}">Full Name *</label>
                                            {{ form.fullname }}
                                            {% if form.fullname.errors %}
                                                <div class="text-danger">{{ form.fullname.errors }}</div>
                                            {% endif %}
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="{{ form.gender.id_for_label }}">Gender</label>
                                                    {{ form.gender }}
                                                    {% if form.gender.errors %}
                                                        <div class="text-danger">{{ form.gender.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="{{ form.category.id_for_label }}">Category</label>
                                                    {{ form.category }}
                                                    {% if form.category.errors %}
                                                        <div class="text-danger">{{ form.category.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="{{ form.date_of_birth.id_for_label }}">Date of Birth *</label>
                                            {{ form.date_of_birth }}
                                            {% if form.date_of_birth.errors %}
                                                <div class="text-danger">{{ form.date_of_birth.errors }}</div>
                                            {% endif %}
                                        </div>

                                        <div class="form-group">
                                            <label for="{{ form.aadhar.id_for_label }}">Aadhaar Number</label>
                                            {{ form.aadhar }}
                                            {% if form.aadhar.errors %}
                                                <div class="text-danger">{{ form.aadhar.errors }}</div>
                                            {% endif %}
                                        </div>

                                        <div class="form-group">
                                            <label for="{{ form.mobile_number.id_for_label }}">Mobile Number</label>
                                            {{ form.mobile_number }}
                                            {% if form.mobile_number.errors %}
                                                <div class="text-danger">{{ form.mobile_number.errors }}</div>
                                            {% endif %}
                                        </div>

                                        <div class="form-group">
                                            <label for="{{ form.email_id.id_for_label }}">Email ID</label>
                                            {{ form.email_id }}
                                            {% if form.email_id.errors %}
                                                <div class="text-danger">{{ form.email_id.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Academic Information -->
                                    <div class="col-md-6">
                                        <h5 class="text-primary mb-3">Academic Information</h5>
                                        
                                        <div class="form-group">
                                            <label for="{{ form.date_of_admission.id_for_label }}">Date of Admission *</label>
                                            {{ form.date_of_admission }}
                                            {% if form.date_of_admission.errors %}
                                                <div class="text-danger">{{ form.date_of_admission.errors }}</div>
                                            {% endif %}
                                        </div>

                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="form-group">
                                                    <label for="{{ form.current_class.id_for_label }}">Class</label>
                                                    {{ form.current_class }}
                                                    {% if form.current_class.errors %}
                                                        <div class="text-danger">{{ form.current_class.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="{{ form.section.id_for_label }}">Section</label>
                                                    {{ form.section }}
                                                    {% if form.section.errors %}
                                                        <div class="text-danger">{{ form.section.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="{{ form.current_status.id_for_label }}">Status</label>
                                            {{ form.current_status }}
                                            {% if form.current_status.errors %}
                                                <div class="text-danger">{{ form.current_status.errors }}</div>
                                            {% endif %}
                                        </div>

                                        <h5 class="text-primary mb-3 mt-4">Parent Information</h5>
                                        
                                        <div class="form-group">
                                            <label for="{{ form.Father_name.id_for_label }}">Father's Name *</label>
                                            {{ form.Father_name }}
                                            {% if form.Father_name.errors %}
                                                <div class="text-danger">{{ form.Father_name.errors }}</div>
                                            {% endif %}
                                        </div>

                                        <div class="form-group">
                                            <label for="{{ form.Father_mobile_number.id_for_label }}">Father's Mobile *</label>
                                            {{ form.Father_mobile_number }}
                                            {% if form.Father_mobile_number.errors %}
                                                <div class="text-danger">{{ form.Father_mobile_number.errors }}</div>
                                            {% endif %}
                                        </div>

                                        <div class="form-group">
                                            <label for="{{ form.Father_aadhar.id_for_label }}">Father's Aadhaar</label>
                                            {{ form.Father_aadhar }}
                                            {% if form.Father_aadhar.errors %}
                                                <div class="text-danger">{{ form.Father_aadhar.errors }}</div>
                                            {% endif %}
                                        </div>

                                        <div class="form-group">
                                            <label for="{{ form.Mother_name.id_for_label }}">Mother's Name</label>
                                            {{ form.Mother_name }}
                                            {% if form.Mother_name.errors %}
                                                <div class="text-danger">{{ form.Mother_name.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Address and Other Information -->
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="{{ form.address.id_for_label }}">Address</label>
                                            {{ form.address }}
                                            {% if form.address.errors %}
                                                <div class="text-danger">{{ form.address.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="{{ form.others.id_for_label }}">Other Information</label>
                                            {{ form.others }}
                                            {% if form.others.errors %}
                                                <div class="text-danger">{{ form.others.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Photo Upload -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="{{ form.passport.id_for_label }}">Student Photo</label>
                                            {{ form.passport }}
                                            {% if form.passport.errors %}
                                                <div class="text-danger">{{ form.passport.errors }}</div>
                                            {% endif %}
                                            {% if student and student.passport %}
                                                <div class="mt-2">
                                                    <img src="{{ student.passport.url }}" alt="Current Photo" class="img-thumbnail" style="max-width: 150px;">
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> 
                                    {% if student %}Update Student{% else %}Admit Student{% endif %}
                                </button>
                                <a href="{% url 'teacher_student_table' %}" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Load sections when class changes
function loadSections(classId) {
    if (classId) {
        fetch(`/teacher-dashboard/api/class/${classId}/sections/`)
            .then(response => response.json())
            .then(data => {
                const sectionSelect = document.querySelector('select[name="section"]');
                sectionSelect.innerHTML = '<option value="">-- Select Section --</option>';
                
                if (data.sections) {
                    data.sections.forEach(section => {
                        const option = document.createElement('option');
                        option.value = section;
                        option.textContent = section;
                        sectionSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading sections:', error);
            });
    }
}

// Add event listener to class select
document.addEventListener('DOMContentLoaded', function() {
    const classSelect = document.querySelector('select[name="current_class"]');
    if (classSelect) {
        classSelect.addEventListener('change', function() {
            loadSections(this.value);
        });
    }
});
</script>
{% endblock %}
